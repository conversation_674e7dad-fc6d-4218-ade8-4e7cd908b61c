@extends('layouts.app')

@section('content')
    <div x-data="initCheatsheet()">
        <div class="grid grid-cols-3 w-fit mx-auto">
            <div class="border-2 text-center px-2" aria-label="Arch Type" title="Arch Type" @click="tab = 'at'">
                AT
            </div>
            <div class="border-2 text-center px-2" aria-label="Keywords" title="Keywords" @click="tab = 'kw'">KW
            </div>
            <div class="border-2 text-center px-2" aria-label="Build Template" title="Build Template"
                 @click="tab = 'bt'">BT
            </div>
        </div>
        <div x-show="tab === 'at'" class="w-full">
            @foreach($archtypes as $id => $archtype)
                <div x-show="archtype === {{ $id }}" class="flex">
                    <div class="flex flex-wrap">
                        @foreach($archtype['colors'] as $color)
                            @foreach($archtype['cards'][$color] as $card)
                                <div class="w-16 overflow-x-visible" @hover="card = '{{ urlencode($card['name']) }}'"
                                     @click="card = '{{ urlencode($card['name']) }}'" :class="{'w-32': card === '{{ urlencode($card['name']) }}'}">
                                    <div class="relative">
                                        <img src="{{ $card['url'] }}" alt="{{ $card['name'] }}"
                                             class="left-0 w-32 h-44 max-w-none">
                                    </div>
                                </div>
                            @endforeach
                            @foreach($cardsPerColor[$color] as $card)
                                <div class="w-16 overflow-x-visible" @hover="card = '{{ urlencode($card['name']) }}'"
                                     @click="card = '{{ urlencode($card['name']) }}'" :class="{'w-32': card === '{{ urlencode($card['name']) }}'}">
                                    <div class="relative">
                                        <img src="{{ $card['url'] }}" alt="{{ $card['name'] }}"
                                             class="left-0 w-32 h-44 max-w-none">
                                    </div>
                                </div>
                            @endforeach
                        @endforeach
                        @foreach($archtype['cards']['multi'] as $card)
                            <div class="w-16 overflow-x-visible" @hover="card = '{{ urlencode($card['name']) }}'"
                                 @click="card = '{{ urlencode($card['name']) }}'" :class="{'w-32': card === '{{ urlencode($card['name']) }}'}">
                                <div class="relative">
                                    <img src="{{ $card['url'] }}" alt="{{ $card['name'] }}"
                                         class="left-0 w-32 h-44 max-w-none">
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
            <div class="mx-auto flex justify-center gap-2">
                @foreach($archtypes as $id => $archtype)
                    <div @click="archtype = {{ $id }}">
                        @foreach($archtype['colors'] as $color)
                            <div class="border-2 {{ $color }} rounded-full w-8 h-8">&nbsp;</div>
                        @endforeach
                    </div>
                @endforeach
            </div>
        </div>
        <div x-show="tab === 'kw'">
            keywords
        </div>
        <div x-show="tab === 'bt'">
            build-template
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function initCheatsheet() {
            return {
                tab: 'at',
                archtype: 0,
                card: null,
            };
        }
    </script>
@endsection
